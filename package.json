{"name": "zaiply-clickdummy", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@neondatabase/serverless": "^1.0.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@tanstack/react-table": "^8.21.3", "better-auth": "^1.3.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "framer-motion": "^12.23.12", "lucide-react": "^0.539.0", "next": "15.4.6", "next-themes": "^0.4.6", "ogl": "^1.0.11", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "stripe": "^18.4.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.16", "zustand": "^5.0.7"}, "devDependencies": {"@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "drizzle-kit": "^0.31.4", "tailwindcss": "^4", "tsx": "^4.20.3", "tw-animate-css": "^1.3.6", "typescript": "^5"}}