import { pgTable, text, timestamp, boolean } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { user } from "@/db/schema/auth.schema";
import { payment } from "./payment.schema";

export const tenant = pgTable("tenant", {
	id: text("id")
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID()),
	name: text("name").notNull(),
	email: text("email").notNull().unique(),
	createdAt: timestamp("created_at")
		.$defaultFn(() => new Date())
		.notNull(),
	updatedAt: timestamp("updated_at")
		.$defaultFn(() => new Date())
		.notNull(),
	isActive: boolean("is_active")
		.$defaultFn(() => true)
		.notNull(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
});

export const tenantRelations = relations(tenant, ({ one, many }) => ({
	user: one(user, {
		fields: [tenant.userId],
		references: [user.id],
	}),
	payments: many(payment),
}));

export type Tenant = typeof tenant.$inferSelect;
export type TenantWithPayments = Tenant & {
	payments: Array<typeof payment.$inferSelect>;
};
