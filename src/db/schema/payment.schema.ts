import {
	boolean,
	pgTable,
	text,
	timestamp,
	doublePrecision,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { user } from "@/db/schema/auth.schema";
import { tenant } from "./tenant.schema";
import type { Tenant } from "./tenant.schema";

export const payment = pgTable("payment", {
	id: text("id")
		.primaryKey()
		.$defaultFn(() => crypto.randomUUID()),
	createdAt: timestamp("created_at")
		.$defaultFn(() => new Date())
		.notNull(),
	updatedAt: timestamp("updated_at")
		.$defaultFn(() => new Date())
		.notNull(),
	amount: text("amount").notNull(),
	currency: text("currency").notNull(),
	description: text("description").notNull(),
	status: text("status").notNull(),
	stripePaymentIntentId: text("stripe_payment_intent_id").notNull(),
	stripeCustomerId: text("stripe_customer_id"),
	tenantId: text("tenant_id")
		.notNull()
		.references(() => tenant.id, { onDelete: "cascade" }), // receiver
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	checkoutUrl: text("checkout_url"),
});

export const paymentRelations = relations(payment, ({ one }) => ({
	tenant: one(tenant, {
		fields: [payment.tenantId],
		references: [tenant.id],
	}),
	user: one(user, {
		fields: [payment.userId],
		references: [user.id],
	}),
}));

export type Payment = typeof payment.$inferSelect;
export type PaymentWithTenant = Payment & { tenant: Tenant };
