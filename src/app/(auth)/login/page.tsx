import type { NextPage } from "next";
import { redirect } from "next/navigation";
import { headers } from "next/headers";
import { LoginForm } from "@/components/forms/login-form";
import { auth } from "@/lib/auth";

const LoginPage: NextPage = async () => {
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (session) {
		return redirect("/");
	}

	return (
		<aside className="w-full max-w-md space-y-12">
			<div className="space-y-1.5">
				<div className="text-xl">Log in to Zaiply&trade;</div>
				<p className="text-sm text-muted-foreground text-balance">
					Hello again! We&apos;re glad you&apos;re back.
				</p>
			</div>
			<LoginForm redirectUrl={"/"} />
		</aside>
	);
};

export default LoginPage;
