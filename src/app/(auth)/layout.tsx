import { Aurora } from "@/components/bits/aurora";

export default function AuthLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<div className="container mx-auto min-h-svh grid grid-cols-2 items-center">
			{children}
			<div className="absolute inset-0 -z-10">
				<div className="bg-gradient-to-r from-background to-transparent absolute inset-0" />
				<div className="bg-gradient-to-r from-background to-transparent absolute inset-0" />
				<Aurora
					colorStops={["#bef264", "#a3e635", "#d9f99d"]}
					blend={1}
					amplitude={1.0}
					speed={0.5}
				/>
			</div>
		</div>
	);
}
