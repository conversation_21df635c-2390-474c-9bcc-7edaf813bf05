import { RegisterForm } from "@/components/forms/register-form";
import type { NextPage } from "next";

const CreateFirstUserPage: NextPage = async () => {
	return (
		<aside className="w-full max-w-md space-y-12">
			<div className="space-y-1.5">
				<div className="text-xl">Welcome to Zaiply&trade;</div>
				<p className="text-sm text-muted-foreground text-balance">
					Create your account to get started getting payed
				</p>
			</div>
			<RegisterForm redirectUrl={"/"} />
		</aside>
	);
};

export default CreateFirstUserPage;
