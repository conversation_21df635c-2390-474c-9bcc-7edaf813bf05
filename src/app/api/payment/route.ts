import { db } from "@/db";
import { payment, user } from "@/db/schema";
import { eq } from "drizzle-orm/sql/expressions/conditions";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

export async function GET(request: Request) {
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session?.user.id) {
		return new Response(JSON.stringify({ error: "Unauthorized" }), {
			status: 401,
			headers: { "Content-Type": "application/json" },
		});
	}

	const payments = await db.query.payment.findMany({
		where: eq(payment.userId, session?.user.id),
		with: { tenant: true },
	});

	return new Response(JSON.stringify(payments), {
		status: 200,
		headers: { "Content-Type": "application/json" },
	});
}
