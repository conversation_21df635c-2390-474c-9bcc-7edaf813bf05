import {NextResponse} from "next/server";
import {db} from "@/db";
import {eq} from "drizzle-orm/sql/expressions/conditions";
import {payment} from "@/db/schema";

export async function GET(request: Request, { params }: { params: { id: string } }) {
    const paymentId = params.id;

    const fetchedPayment = await db.query.payment.findFirst({
        where: eq(payment.id, paymentId)
    });

    if (!fetchedPayment || fetchedPayment.status !== 'unpaid') {
        return NextResponse.json({ error: "Payment not found" }, { status: 404 });
    }

    return NextResponse.json(fetchedPayment, { status: 200 });
}
