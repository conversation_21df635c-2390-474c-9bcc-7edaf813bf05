import {NextResponse} from "next/server";
import {db} from "@/db";
import {payment} from "@/db/schema";
import {auth} from "@/lib/auth";
import {headers} from "next/headers";

export async function POST(request: Request) {
    const body = await request.json();
    const { amount, currency, description, tenant_id } = body;
    if (!amount || !currency || !tenant_id) {
        return NextResponse.json({ error: "Amount, currency and tenant_id are required" }, { status: 400 });
    }

    const userSession = await auth.api.getSession({
        headers: await headers()
    });

    if (!userSession?.user.id) {
        return new Response(JSON.stringify({error: "Unauthorized"}), {
            status: 401,
            headers: {"Content-Type": "application/json"}
        });
    }

    const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);
    const stripeSession = await stripe.checkout.sessions.create({
        payment_method_types: ['card'],
        line_items: [{
            price_data: {
                currency: currency,
                product_data: {
                    name: description || 'Payment',
                },
                unit_amount: amount * 100,
            },
            quantity: 1,
        }],
        mode: 'payment',
        success_url: `${process.env.NEXT_PUBLIC_BASE_URL}/success?session_id={CHECKOUT_SESSION_ID}`,
        cancel_url: `${process.env.NEXT_PUBLIC_BASE_URL}/cancel`,
        metadata: {
            tenant_id,
        },
    });

    await db.insert(payment).values({
        amount: String(amount),
        currency: String(currency),
        description,
        tenantId: tenant_id,
        userId: userSession.user.id,
        stripePaymentIntentId: stripeSession.id,
        stripeCustomerId: stripeSession.customerId,
        checkoutUrl: stripeSession.url,
        status: 'unpaid',
    }).catch((error) => {
        console.error("Error inserting payment record:", error);
        return NextResponse.json({ error: "Failed to create payment record" }, { status: 500 });
    })

    return NextResponse.json({ sessionId: stripeSession.id }, { status: 200 });
}
