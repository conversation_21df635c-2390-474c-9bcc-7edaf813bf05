import { auth } from "@/lib/auth";
import { headers } from "next/headers";
import { db } from "@/db";
import { eq } from "drizzle-orm/sql/expressions/conditions";
import { tenant } from "@/db/schema";

export async function GET(req: Request) {
	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session?.user.id) {
		return new Response(JSON.stringify({ error: "Unauthorized" }), {
			status: 401,
			headers: { "Content-Type": "application/json" },
		});
	}

	// Check if the request wants to include payments
	const url = new URL(req.url);
	const includePayments = url.searchParams.get("includePayments") === "true";

	const tenants = await db.query.tenant.findMany({
		where: eq(tenant.userId, session?.user.id),
		with: includePayments
			? {
					payments: {
						orderBy: (payment, { desc }) => [desc(payment.createdAt)],
					},
				}
			: undefined,
	});

	return new Response(JSON.stringify(tenants), {
		status: 200,
		headers: { "Content-Type": "application/json" },
	});
}
