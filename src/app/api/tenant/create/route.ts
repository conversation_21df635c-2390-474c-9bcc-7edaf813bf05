import {NextResponse} from "next/server";
import {db} from "@/db";
import {tenant} from "@/db/schema";
import {AddTenantFormSchema} from "@/components/forms/add-tenant-form.schema";
import {auth} from "@/lib/auth";
import {headers} from "next/headers";

export async function POST(req: Request) {
    if (!req.headers.get("content-type")?.includes("application/json")) {
        return NextResponse.json(
            {error: "Content-Type must be application/json"},
            {status: 415},
        );
    }

    const session = await auth.api.getSession({
        headers: await headers()
    });

    if (!session?.user.id) {
        return new Response(JSON.stringify({error: "Unauthorized"}), {
            status: 401,
            headers: {"Content-Type": "application/json"}
        });
    }

    const json = await req.json();

    const result = AddTenantFormSchema.safeParse(json);
    if (!result.success) {
        return NextResponse.json(
            {error: "Invalid request body", details: result.error},
            {status: 400},
        );
    }

    await db.insert(tenant).values(
        {
            name: result.data.name,
            email: result.data.email,
            userId: session?.user.id,
        }
    ).catch(console.error);

    return NextResponse.json({success: true}, {status: 200});
}
