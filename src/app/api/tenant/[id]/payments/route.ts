import { NextResponse } from "next/server";
import { db } from "@/db";
import { payment, tenant } from "@/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "@/lib/auth";
import { headers } from "next/headers";

export async function GET(
	request: Request,
	{ params }: { params: { id: string } }
) {
	const tenantId = params.id;

	const session = await auth.api.getSession({
		headers: await headers(),
	});

	if (!session?.user.id) {
		return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
	}

	// First verify that the tenant belongs to the current user
	const tenantExists = await db.query.tenant.findFirst({
		where: and(eq(tenant.id, tenantId), eq(tenant.userId, session.user.id)),
	});

	if (!tenantExists) {
		return NextResponse.json(
			{ error: "Tenant not found or access denied" },
			{ status: 404 }
		);
	}

	// Get all payments for this tenant
	const payments = await db.query.payment.findMany({
		where: and(
			eq(payment.tenantId, tenantId),
			eq(payment.userId, session.user.id)
		),
		with: {
			tenant: true,
		},
		orderBy: (payment, { desc }) => [desc(payment.createdAt)],
	});

	return NextResponse.json(payments, { status: 200 });
}
