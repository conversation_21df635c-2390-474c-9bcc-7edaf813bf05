"use client";

import { TenantsTable } from "@/components/tables/tenants-table";
import { columns } from "@/components/tables/tenants-table/columns";
import { Button } from "@/components/ui/button";
import type { Tenant } from "@/db/schema";
import { useModal } from "@/hooks/use-modal";
import { PlusIcon } from "@radix-ui/react-icons";
import type { NextPage } from "next";
import { useEffect, useState } from "react";

const TenantsPage: NextPage = () => {
	const { onOpen } = useModal();
	const [tenants, setTenants] = useState<Tenant[]>([]);

	useEffect(() => {
		(async () => {
			const res = await fetch("/api/tenant");
			const data = await res.json();
			setTenants(data);
		})();
	}, []);

	return (
		<div className="space-y-12">
			<div className="flex items-end justify-between">
				<div className="space-y-2.5">
					<h1 className="text-3xl font-semibold">Clients</h1>
					<p className="text-muted-foreground max-w-xl text-balance">
						Hier kannst du deine Kunden verwalten.
					</p>
				</div>
				<div>
					<Button variant={"outline"} onClick={() => onOpen("addTenant")}>
						<PlusIcon />
						Kunden hinzufügen
					</Button>
				</div>
			</div>
			<div>
				<TenantsTable data={tenants} columns={columns} />
			</div>
		</div>
	);
};

export default TenantsPage;
