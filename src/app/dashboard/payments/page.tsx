"use client";

import { But<PERSON> } from "@/components/ui/button";
import type { PaymentWithTenant } from "@/db/schema";
import { useModal } from "@/hooks/use-modal";
import { CopyIcon, Link1Icon, PlusIcon } from "@radix-ui/react-icons";
import type { NextPage } from "next";
import Link from "next/link";
import { useEffect, useState } from "react";

const PaymentsPage: NextPage = () => {
	const { onOpen } = useModal();
	const [payments, setPayments] = useState<PaymentWithTenant[]>([]);

	useEffect(() => {
		(async () => {
			const res = await fetch("/api/payment");
			const data = await res.json();
			setPayments(data);
		})();
	}, []);

	return (
		<div className="space-y-12">
			<div className="flex items-end justify-between">
				<div className="space-y-2.5">
					<h1 className="text-3xl font-semibold">Rechnungen</h1>
					<p className="text-muted-foreground max-w-xl text-balance">
						Hier kannst du deine Rechnungen verwalten.
					</p>
				</div>
				<div>
					<Button variant={"outline"} onClick={() => onOpen("addPayment")}>
						<PlusIcon />
						Rechnung hinzufügen
					</Button>
				</div>
			</div>
			<div className="grid divide-y divide-muted">
				{payments.map((payment) => (
					<div
						key={payment.id}
						className="flex items-center justify-between border rounded-md px-6 py-4"
					>
						<div className="flex items-center gap-x-4">
							<span>{payment.tenant.name}</span>
							<span className="h-5 rotate-12 w-px bg-muted" />
							<span>{payment.description}</span>
						</div>
						<div className="flex items-center gap-x-4">
							<Button
								variant={"outline"}
								onClick={() => {
									navigator.clipboard.writeText(payment.checkoutUrl || "");
								}}
							>
								<CopyIcon />
								Link kopieren
							</Button>
							<Button asChild>
								<Link href={payment.checkoutUrl || "#"}>
									<Link1Icon />
									Zu Rechnung
								</Link>
							</Button>
						</div>
					</div>
				))}
			</div>
		</div>
	);
};

export default PaymentsPage;
