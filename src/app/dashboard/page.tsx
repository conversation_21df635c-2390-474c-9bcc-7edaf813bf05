"use client";
import {auth} from "@/lib/auth";
import {authClient} from "@/lib/auth.client";

export default function Page() {
    const session = authClient.useSession();

    return (
        <div className="flex flex-row w-full h-full gap-4 p-4">
            <aside className="w-75">
                <div className="space-y-1.5">
                    <div className="text-xl">Welcome to your dashboard, <b>{session?.data?.user.name}</b>!
                    </div>
                    <p className="text-sm text-muted-foreground text-balance">
                        This is where you can manage your account, view your activity, and more.
                    </p>
                </div>
            </aside>
            <aside className="w-50">
                <div className="space-y-1.5">
                    <div className="text-xl">Your Tenants</div>
                    <p className="text-sm text-muted-foreground text-balance">
                        Manage your tenants and their settings.
                    </p>
                </div>
                {/* Here you can add components to display tenants, payments, etc. */}
            </aside>
        </div>
    )
}
