"use client";

import { useEffect, useState } from "react";
import { AddTenantModal } from "@/components/modals/add-tenant-modal";
import { AddPaymentModal } from "../modals/add-payment-modal";

const ModalProvider: React.FC = () => {
	const [isMounted, setIsMounted] = useState(false);

	useEffect(() => {
		setIsMounted(true);
	}, []);

	if (!isMounted) return null;

	return (
		<>
			<AddTenantModal />

			<AddPaymentModal />
		</>
	);
};

export default ModalProvider;
