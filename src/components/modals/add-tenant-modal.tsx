"use client";

import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useModal } from "@/hooks/use-modal";
import { AddTenantForm } from "../forms/add-tenant-form";

export const AddTenantModal = () => {
	const { isOpen, onClose, type } = useModal();

	const isModalOpen = isOpen && type === "addTenant";

	const handleClose = () => {
		onClose();
	};

	return (
		<Dialog open={isModalOpen} onOpenChange={handleClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Kunden hinzufügen</DialogTitle>
					<DialogDescription>Füge einen neuen Kunden hinzu.</DialogDescription>
				</DialogHeader>
				<AddTenantForm callback={handleClose} />
			</DialogContent>
		</Dialog>
	);
};
