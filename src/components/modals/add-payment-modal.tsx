"use client";

import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { useModal } from "@/hooks/use-modal";
import { AddPaymentForm } from "../forms/add-payment-form";

export const AddPaymentModal = () => {
	const { isOpen, onClose, type } = useModal();

	const isModalOpen = isOpen && type === "addPayment";

	const handleClose = () => {
		onClose();
	};

	return (
		<Dialog open={isModalOpen} onOpenChange={handleClose}>
			<DialogContent>
				<DialogHeader>
					<DialogTitle>Rechnung hinzufügen</DialogTitle>
					<DialogDescription>Füge eine neue Rechnung hinzu.</DialogDescription>
				</DialogHeader>
				<AddPaymentForm callback={handleClose} />
			</DialogContent>
		</Dialog>
	);
};
