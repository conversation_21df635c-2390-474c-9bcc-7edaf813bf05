"use client";

import type { ComponentProps, FC } from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { authClient } from "@/lib/auth.client";
import { cn } from "@/lib/utils";

interface ILoginFormProps extends ComponentProps<"form"> {
	redirectUrl?: string;
}

const formSchema = z.object({
	email: z.email(),
	password: z.string(),
	rememberMe: z.boolean().optional(),
});

export const LoginForm: FC<ILoginFormProps> = ({
	className,
	redirectUrl = "/",
	...props
}) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			email: "",
			password: "",
			rememberMe: true,
		},
	});

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		setIsLoading(true);
		setError("");

		try {
			const { data, error } = await authClient.signIn.email({
				email: values.email,
				password: values.password,
				rememberMe: true,
			});

			if (error) {
				throw new Error(error.message);
			}

			if (!data?.user) {
				throw new Error("Failed to sign in");
			}

			router.push(redirectUrl);
			router.refresh();
			setIsLoading(false);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className={cn("space-y-12", className)}
				{...props}
			>
				<fieldset className="space-y-4" disabled={isLoading}>
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input type="email" autoComplete="webauthn" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Password</FormLabel>
								<FormControl>
									<Input type="password" autoComplete="webauthn" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<FormControl>
									<div className="flex items-center space-x-2">
										<Checkbox {...field} />
										<FormLabel>Remember me</FormLabel>
									</div>
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					{error && (
						<Badge
							variant={"destructive"}
							className="text-destructive-foreground"
						>
							{error}
						</Badge>
					)}
				</fieldset>
				<Button type="submit" disabled={isLoading}>
					{isLoading ? "Logging in..." : "Log in to your account"}
				</Button>
			</form>
		</Form>
	);
};
