"use client";

import type { ComponentProps, FC } from "react";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { cn } from "@/lib/utils";
import { AddPaymentFormSchema } from "./add-payment-form.schema";
import type { Tenant } from "@/db/schema";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "../ui/select";

interface IAddPaymentFormProps extends ComponentProps<"form"> {
	callback?: () => void;
}

export const AddPaymentForm: FC<IAddPaymentFormProps> = ({
	className,
	callback,
	...props
}) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");
	const [tenants, setTenants] = useState<Tenant[]>([]);

	useEffect(() => {
		(async () => {
			const res = await fetch("/api/tenant");
			const data = await res.json();
			setTenants(data);
		})();
	}, []);

	const form = useForm<z.infer<typeof AddPaymentFormSchema>>({
		resolver: zodResolver(AddPaymentFormSchema),
		defaultValues: {
			amount: "0.00",
			currency: "EUR",
			description: "",
			tenant_id: "",
		},
	});

	const onSubmit = async (values: z.infer<typeof AddPaymentFormSchema>) => {
		setIsLoading(true);
		setError("");

		try {
			const res = await fetch("/api/payment/create", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(values),
			});

			if (!res.ok) {
				setError("Failed to create payment");
				return;
			}

			if (callback) callback();
			router.refresh();
			setIsLoading(false);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className={cn("space-y-12", className)}
				{...props}
			>
				<fieldset className="space-y-4" disabled={isLoading}>
					<FormField
						control={form.control}
						name="amount"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Betrag</FormLabel>
								<FormControl>
									<Input type="text" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="currency"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Währung</FormLabel>
								<FormControl>
									<Input type="text" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="description"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Beschreibung</FormLabel>
								<FormControl>
									<Input type="text" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="tenant_id"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Kunde</FormLabel>
								<Select
									onValueChange={field.onChange}
									defaultValue={field.value}
								>
									<FormControl>
										<SelectTrigger className="w-full">
											<SelectValue placeholder="Kunde auswählen" />
										</SelectTrigger>
									</FormControl>
									<SelectContent>
										{tenants.map((tenant) => (
											<SelectItem key={tenant.id} value={tenant.id}>
												{tenant.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
								<FormMessage />
							</FormItem>
						)}
					/>
					{error && (
						<Badge
							variant={"destructive"}
							className="text-destructive-foreground"
						>
							{error}
						</Badge>
					)}
				</fieldset>
				<Button type="submit" disabled={isLoading}>
					{isLoading ? "Kunde wird hinzugefügt..." : "Kunde hinzufügen"}
				</Button>
			</form>
		</Form>
	);
};
