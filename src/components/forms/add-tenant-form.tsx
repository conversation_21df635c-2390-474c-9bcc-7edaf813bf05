"use client";

import type { ComponentProps, FC } from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import { cn } from "@/lib/utils";
import { AddTenantFormSchema } from "./add-tenant-form.schema";

interface IAddTenantFormProps extends ComponentProps<"form"> {
	callback?: () => void;
}

export const AddTenantForm: FC<IAddTenantFormProps> = ({
	className,
	callback,
	...props
}) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");

	const form = useForm<z.infer<typeof AddTenantFormSchema>>({
		resolver: zodResolver(AddTenantFormSchema),
		defaultValues: {
			name: "",
			email: "",
		},
	});

	const onSubmit = async (values: z.infer<typeof AddTenantFormSchema>) => {
		setIsLoading(true);
		setError("");

		try {
			const res = await fetch("/api/tenant/create", {
				method: "POST",
				headers: {
					"Content-Type": "application/json",
				},
				body: JSON.stringify(values),
			});

			if (!res.ok) {
				setError("Failed to create tenant");
				return;
			}

			if (callback) callback();
			router.refresh();
			setIsLoading(false);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className={cn("space-y-12", className)}
				{...props}
			>
				<fieldset className="space-y-4" disabled={isLoading}>
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Name</FormLabel>
								<FormControl>
									<Input type="text" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input type="email" autoComplete="webauthn" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					{error && (
						<Badge
							variant={"destructive"}
							className="text-destructive-foreground"
						>
							{error}
						</Badge>
					)}
				</fieldset>
				<Button type="submit" disabled={isLoading}>
					{isLoading ? "Kunde wird hinzugefügt..." : "Kunde hinzufügen"}
				</Button>
			</form>
		</Form>
	);
};
