"use client";

import type { ComponentProps, FC } from "react";
import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { authClient } from "@/lib/auth.client";
import { cn } from "@/lib/utils";

interface IRegisterFormProps extends ComponentProps<"form"> {
	redirectUrl?: string;
}

const formSchema = z.object({
	name: z
		.string()
		.min(3, { message: "Username must be at least 3 characters." }),
	email: z.string().email({ message: "Must be a valid email." }),
	password: z
		.string()
		.min(8, { message: "Password must be at least 8 characters." }),
	repeatPassword: z.string(),
});

export const RegisterForm: FC<IRegisterFormProps> = ({
	className,
	redirectUrl = "/",
	...props
}) => {
	const router = useRouter();
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState("");

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			name: "",
			email: "",
			password: "",
			repeatPassword: "",
		},
	});

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		setIsLoading(true);
		setError("");

		if (values.password !== values.repeatPassword) {
			setError("Passwords do not match");
			setIsLoading(false);
			return;
		}

		try {
			const { data, error } = await authClient.signUp.email({
				email: values.email,
				password: values.password,
				name: values.name,
			});

			if (error) {
				throw new Error(error.message);
			}

			if (!data?.user) {
				throw new Error("Failed to sign in");
			}

			router.push(redirectUrl);
			router.refresh();
			setIsLoading(false);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : "An unknown error occurred",
			);
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<Form {...form}>
			<form
				onSubmit={form.handleSubmit(onSubmit)}
				className={cn("space-y-12", className)}
				{...props}
			>
				<fieldset className="space-y-4" disabled={isLoading}>
					<FormField
						control={form.control}
						name="name"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Name</FormLabel>
								<FormControl>
									<Input {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="email"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Email</FormLabel>
								<FormControl>
									<Input type="email" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="password"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Password</FormLabel>
								<FormControl>
									<Input type="password" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					<FormField
						control={form.control}
						name="repeatPassword"
						render={({ field }) => (
							<FormItem>
								<FormLabel>Repeat Password</FormLabel>
								<FormControl>
									<Input type="password" {...field} />
								</FormControl>
								<FormMessage />
							</FormItem>
						)}
					/>
					{error && (
						<Badge
							variant={"destructive"}
							className="text-destructive-foreground"
						>
							{error}
						</Badge>
					)}
				</fieldset>
				<Button type="submit" disabled={isLoading}>
					{isLoading ? "Creating account..." : "Create Admin Account"}
				</Button>
			</form>
		</Form>
	);
};
