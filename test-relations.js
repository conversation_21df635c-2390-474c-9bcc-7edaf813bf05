// Simple test script to verify database relations
import { db } from "./src/db/index.js";
import { payment, tenant } from "./src/db/schema/index.js";
import { eq } from "drizzle-orm";

async function testRelations() {
  try {
    console.log("Testing payment-tenant relations...");
    
    // Test 1: Query payments with tenant relation
    const paymentsWithTenant = await db.query.payment.findMany({
      with: {
        tenant: true,
      },
      limit: 5,
    });
    
    console.log("Payments with tenant:", JSON.stringify(paymentsWithTenant, null, 2));
    
    // Test 2: Query tenants with payments relation
    const tenantsWithPayments = await db.query.tenant.findMany({
      with: {
        payments: true,
      },
      limit: 5,
    });
    
    console.log("Tenants with payments:", JSON.stringify(tenantsWithPayments, null, 2));
    
    console.log("Relations test completed successfully!");
  } catch (error) {
    console.error("Error testing relations:", error);
  }
}

testRelations();
